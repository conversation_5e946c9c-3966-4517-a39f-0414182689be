// src/hooks/useNativeToken.ts
'use client'

import { useEffect } from 'react'
import { useNativeStore } from '@/stores/useNativeStore'

export function useNativeToken(timeoutDuration = 1000) {
  const setNativeData = useNativeStore((s) => s.setNativeData)
  const setTimeoutReached = useNativeStore((s) => s.setTimeoutReached)

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data)

        setNativeData({
          token: data.token,
          locale: data.locale,
          userId: data.userId,
          versionAppName: data.versionAppName,
          ...(data.top && { top: data.top }),
          ...(data.bottom && { bottom: data.bottom })
        })
      } catch (error) {
        console.warn('Invalid token message:', error)
      }
    }

    window.addEventListener('message', handleMessage)

    if ((window as Window & { ReactNativeOS?: string }).ReactNativeOS === 'android') {
      document.addEventListener('message', handleMessage as EventListener)
    } else {
      window.addEventListener('message', handleMessage)
    }
    const timeoutId = setTimeout(() => {
      setTimeoutReached()
    }, timeoutDuration)

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('message', handleMessage)
    }
  }, [timeoutDuration, setNativeData, setTimeoutReached])
}
