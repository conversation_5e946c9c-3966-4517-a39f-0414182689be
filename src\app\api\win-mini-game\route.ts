import { NextRequest, NextResponse } from 'next/server'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface WinMiniGameRequest {
  accessKey: string
  userId: string
  level: number
}

interface WinMiniGameResponse {
  isWinning: boolean
}

export async function POST(req: NextRequest) {
  try {
    const body: WinMiniGameRequest = await req.json()
    console.log('Win Mini Game API - Incoming Request:', body)

    // Validate required fields
    if (!body.accessKey || !body.userId || typeof body.level !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields: accessKey, userId, level' },
        { status: 400 }
      )
    }

    // Validate access key
    if (body.accessKey !== 'abcdefg') {
      return NextResponse.json(
        { error: 'Invalid access key' },
        { status: 401 }
      )
    }

    // Check if user exists and is on the current level
    const isWinning = UserLevelManager.isUserOnCurrentLevel(body.userId, body.level)

    const response: WinMiniGameResponse = {
      isWinning
    }

    console.log('Win Mini Game API - Response:', response)
    return NextResponse.json(response)

  } catch (error) {
    console.error('Win Mini Game API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
