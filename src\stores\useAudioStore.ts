// src/stores/useAudioStore.ts
'use client'

import { create } from 'zustand'

interface AudioFiles {
  touch: string
  collectAward: string
  win: string
  lose: string
  backgroundMusic: string
}

const AUDIO_FILES: AudioFiles = {
  touch: '/audio/NhacNen_FullTrack.wav',
  collectAward: '/Game Fail.wav',
  win: '/Game Win.mp3',
  lose: '/Game Fail.wav',
  backgroundMusic: '/audio/NhacNen_FullTrack.wav'
}

interface AudioState {
  isMuted: boolean
  isBackgroundMusicPlaying: boolean
  hasUserInteracted: boolean
  init: () => void
  playTouchSound: () => void
  playCollectAwardSound: () => void
  playWinSound: () => void
  playLoseSound: () => void
  startBackgroundMusic: () => void
  stopBackgroundMusic: () => void
  pauseBackgroundMusic: () => void
  resumeBackgroundMusic: () => void
  toggleMute: () => void
}

export const useAudioStore = create<AudioState>((set, get) => {
  // Refs stored outside state to avoid re-renders
  const audioRefs: Record<string, HTMLAudioElement> = {}
  let backgroundMusicRef: HTMLAudioElement | null = null

  const playSound = (soundType: keyof Omit<AudioFiles, 'backgroundMusic'>) => {
    if (get().isMuted) return
    const audio = audioRefs[soundType]
    if (audio) {
      audio.currentTime = 0
      audio.play().catch((err) => console.warn(`Failed to play ${soundType}`, err))
    }
  }

  return {
    isMuted: false,
    isBackgroundMusicPlaying: false,
    hasUserInteracted: true, // default true so autoplay works

    init: () => {
      // init sound effects
      Object.entries(AUDIO_FILES).forEach(([key, value]) => {
        if (key !== 'backgroundMusic') {
          const audio = new Audio(value as string)
          audio.preload = 'auto'
          audio.volume = 1
          audioRefs[key] = audio
        }
      })

      // init bgm
      for (const file of AUDIO_FILES.backgroundMusic) {
        try {
          const audio = new Audio(file)
          audio.preload = 'auto'
          audio.volume = 1
          audio.loop = true
          backgroundMusicRef = audio
          break
        } catch {
          console.warn(`Failed to load ${file}`)
        }
      }

      // listen once for interaction
      const handle = () => {
        set({ hasUserInteracted: true })
        document.removeEventListener('click', handle)
        document.removeEventListener('touchstart', handle)
        document.removeEventListener('keydown', handle)
      }
      document.addEventListener('click', handle)
      document.addEventListener('touchstart', handle)
      document.addEventListener('keydown', handle)
    },

    playTouchSound: () => playSound('touch'),
    playCollectAwardSound: () => playSound('collectAward'),
    playWinSound: () => playSound('win'),
    playLoseSound: () => playSound('lose'),

    startBackgroundMusic: () => {
      if (get().isMuted || !backgroundMusicRef || get().isBackgroundMusicPlaying) return
      backgroundMusicRef
        .play()
        .then(() => set({ isBackgroundMusicPlaying: true }))
        .catch((e) => console.warn('BGM play failed', e))
    },

    stopBackgroundMusic: () => {
      if (backgroundMusicRef && get().isBackgroundMusicPlaying) {
        backgroundMusicRef.pause()
        set({ isBackgroundMusicPlaying: false })
      }
    },

    pauseBackgroundMusic: () => {
      if (backgroundMusicRef && get().isBackgroundMusicPlaying) {
        backgroundMusicRef.pause()
      }
    },

    resumeBackgroundMusic: () => {
      if (backgroundMusicRef && get().isBackgroundMusicPlaying && backgroundMusicRef.paused) {
        backgroundMusicRef.play().catch((e) => console.warn('BGM resume failed', e))
      }
    },

    toggleMute: () => {
      set((state) => {
        const newMuted = !state.isMuted
        if (newMuted) {
          Object.values(audioRefs).forEach((a) => (a.volume = 0))
          if (backgroundMusicRef) {
            backgroundMusicRef.volume = 0
            if (state.isBackgroundMusicPlaying) {
              backgroundMusicRef.pause()
              set({ isBackgroundMusicPlaying: false })
            }
          }
        } else {
          Object.values(audioRefs).forEach((a) => (a.volume = 0.7))
          if (backgroundMusicRef) {
            backgroundMusicRef.volume = 0.3
            if (!state.isBackgroundMusicPlaying) {
              backgroundMusicRef
                .play()
                .then(() => set({ isBackgroundMusicPlaying: true }))
                .catch((e) => console.warn('BGM resume failed', e))
            }
          }
        }
        return { isMuted: newMuted }
      })
    }
  }
})
