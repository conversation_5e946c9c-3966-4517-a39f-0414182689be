'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Separator } from '@/components/ui/separator'
import Image from 'next/image'
import MediumDisabledButton from '@/assets/drawer/mediumDisabledButton.svg'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { useState, useRef } from 'react'
import * as htmlToImage from 'html-to-image'
import { useGameStore } from '@/stores/useGameStore'
import { useShareDialogStore } from '@/stores/useShareDialogStore'
import { useRankingGameCampaign } from '@/hooks/useRankingGameCampaign'
import { PostMessageType } from '@/lib/postMessage'

interface ShareDialogProps {
  open: boolean
  onClose: () => void
  level: number
}

const ShareDialog: React.FC<ShareDialogProps> = ({ open, onClose, level }) => {
  const [text, setText] = useState('')
  const { gameCampaignId, username, avatar, passedLevels, currentLevel } = useGameStore()
  const targetRef = useRef<HTMLDivElement>(null) // reference for AspectRatio

  // Convert image to base64 data URL
  const imageToBase64 = async (src: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = document.createElement('img')
      img.crossOrigin = 'anonymous'
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('Could not get canvas context'))
          return
        }
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)
        try {
          const dataURL = canvas.toDataURL('image/png')
          resolve(dataURL)
        } catch (error) {
          reject(error)
        }
      }
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`))
      img.src = src
    })
  }

  // Convert font to base64 data URL
  const fontToBase64 = async (fontPath: string): Promise<string> => {
    try {
      const response = await fetch(fontPath)
      const blob = await response.blob()
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result as string)
        reader.onerror = reject
        reader.readAsDataURL(blob)
      })
    } catch (error) {
      console.warn(`Failed to load font: ${fontPath}`, error)
      return ''
    }
  }

  const currentLevelInfo = passedLevels.find((lvl) => lvl.level === level)
  const score = currentLevelInfo?.score ?? 0

  const { variant } = useShareDialogStore()

  // Get ranking data for dynamic content in rank variant
  const userId = 'x391d6b4ecadb6625af716fe675cc21cf' // TODO: get from auth/session
  const { data: rankingData } = useRankingGameCampaign(userId)

  // Find current user's ranking data
  const currentUserRanking = rankingData?.find((user) => user.name === username)
  const userRank = currentUserRanking?.rank ?? 0
  const userTotalScore = currentUserRanking?.score?.total ?? 0

  const handleExport = async () => {
    if (!targetRef.current) return
    try {
      // Convert all assets to base64 for proper canvas export
      const backgroundImageSrc = variant === 'rank' ? '/share/rankShare.webp' : '/share/winShare.webp'

      console.log('Starting export process...')

      // Convert background image to base64
      console.log('Converting background image:', backgroundImageSrc)
      const backgroundImageBase64 = await imageToBase64(backgroundImageSrc)
      console.log('Background image converted, length:', backgroundImageBase64.length)

      // Convert avatar to base64 if it exists
      let avatarBase64 = ''
      if (avatar) {
        try {
          console.log('Converting avatar:', avatar)
          avatarBase64 = await imageToBase64(avatar)
          console.log('Avatar converted, length:', avatarBase64.length)
        } catch (error) {
          console.warn('Failed to convert avatar to base64:', error)
        }
      }

      // Convert fonts to base64 and create embedded font CSS
      console.log('Converting fonts...')
      const [bdStreetFontBase64, signwriterFontBase64] = await Promise.all([
        fontToBase64('/fonts/BDStreetSignSans_Variable.ttf'),
        fontToBase64('/fonts/00003-UTM-AS-Signwriter.ttf')
      ])
      console.log('Fonts converted:', {
        bdStreet: bdStreetFontBase64.length,
        signwriter: signwriterFontBase64.length
      })

      // Temporarily replace images with base64 versions
      const targetElement = targetRef.current
      const bgImages = targetElement.querySelectorAll('img[src*="/share/"]') as NodeListOf<HTMLImageElement>
      const avatarImages = targetElement.querySelectorAll('img[src*="http"]') as NodeListOf<HTMLImageElement>

      // Store original sources
      const originalBgSources: string[] = []
      const originalAvatarSources: string[] = []

      // Replace background images
      bgImages.forEach((img, index) => {
        originalBgSources[index] = img.src
        img.src = backgroundImageBase64
      })

      // Replace avatar images
      if (avatarBase64) {
        avatarImages.forEach((img, index) => {
          originalAvatarSources[index] = img.src
          img.src = avatarBase64
        })
      }

      // Create embedded font styles
      const styleElement = document.createElement('style')
      styleElement.textContent = `
        @font-face {
          font-family: 'BD Street Sign Sans Embedded';
          src: url('${bdStreetFontBase64}') format('truetype');
          font-display: block;
        }
        @font-face {
          font-family: 'UTM AS Signwriter Embedded';
          src: url('${signwriterFontBase64}') format('truetype');
          font-display: block;
        }
        .font-bdstreet {
          font-family: 'BD Street Sign Sans Embedded', 'BD Street Sign Sans', sans-serif !important;
        }
        .font-signwriter {
          font-family: 'UTM AS Signwriter Embedded', 'UTM AS Signwriter', sans-serif !important;
        }
      `
      document.head.appendChild(styleElement)

      // Wait for images and fonts to load
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Export with embedded assets
      const dataUrl = await htmlToImage.toPng(targetRef.current, {
        cacheBust: true,
        quality: 1.0,
        pixelRatio: 3, // Higher resolution for better quality
        backgroundColor: 'transparent',
        skipFonts: false,
        width: targetRef.current.offsetWidth,
        height: targetRef.current.offsetHeight
      })

      // Restore original images
      bgImages.forEach((img, index) => {
        img.src = originalBgSources[index]
      })
      avatarImages.forEach((img, index) => {
        img.src = originalAvatarSources[index]
      })

      // Remove embedded styles
      document.head.removeChild(styleElement)

      const base64Only = dataUrl.replace(/^data:image\/png;base64,/, '')

      // Prepare message data based on variant
      const messageData = {
        type: PostMessageType.SHARE_COMMUNITY,
        gameCampaignId,
        levelNumber: variant === 'rank' ? 0 : level,
        content: text,
        imageBase64: base64Only,
        shareType: variant === 'rank' ? 'BY_RANK' : 'BY_LEVEL'
      }

      // Send message to native app
      if (window.ReactNativeWebView?.postMessage) {
        window.ReactNativeWebView.postMessage(JSON.stringify(messageData))
      }

      console.log('Share message sent:', messageData)

      // Close dialog after successful share
      onClose()
    } catch (err) {
      console.error('Export failed', err)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value.slice(0, 100) // max 100 chars
    setText(value)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className='h-full border-none bg-transparent p-0 shadow-none'
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        {/* Top Bar */}
        <div className='absolute top-0 z-10 flex h-[12.3%] w-full items-end justify-center'>
          <span className='font-bdstreet text-border-orange text-[32px]'>Chia sẻ cộng đồng</span>
        </div>

        {/* Main Center Content */}
        <div className='flex h-full w-full items-center justify-center'>
          <AspectRatio ratio={1560 / 2184} className='relative flex justify-center'>
            <Image src='/share/shareBG.png' alt='Share' fill quality={100} />

            {/* Inner AspectRatio centered vertically */}
            <div className='absolute inset-0 flex flex-col items-center justify-center'>
              <AspectRatio
                ref={targetRef}
                ratio={1560 / 1076}
                className='relative flex flex-col items-center shadow-[3px_4px_150px_15px_rgba(255,255,112,0.3)]'
                style={{
                  fontFamily: 'var(--font-bd-street), var(--font-signwriter), var(--font-montserrat), sans-serif'
                }}
              >
                {/* different background already set above; inner content changes by variant */}
                {variant === 'rank' ? (
                  <>
                    <Image src='/share/rankShare.webp' alt='Share' fill quality={100} />

                    {/* Avatar */}
                    <div className='absolute left-[6%] mt-[6%] w-[12%]'>
                      <AspectRatio ratio={1}>
                        <div className='relative h-full w-full rounded-full bg-gradient-to-b from-[#FFFF70] to-[#F6B936] p-[2px]'>
                          <div className='h-full w-full rounded-full'>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                              <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
                            </Avatar>
                          </div>
                        </div>
                      </AspectRatio>
                    </div>

                    <div className='absolute top-[8.5%] left-[22.5%] z-10 flex h-[17%] w-full items-center'>
                      <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
                        {username}
                      </span>
                    </div>

                    <div className='font-montserrat absolute top-[34.49%] z-10 flex h-[50.6%] w-[91.81%] flex-col items-center'>
                      {/* Top Section */}
                      <div className='flex h-[29%] w-[91.66%] items-center'>
                        <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[13.72px] font-bold text-transparent'>
                          Thành tích
                        </span>
                      </div>

                      {/* Separator */}
                      <Separator orientation='horizontal' className='w-[91.66%] bg-[#535548]' />

                      {/* Bottom Section */}
                      <div className='flex h-[71%] w-[91.66%] flex-col justify-evenly text-[13.72px]'>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Hạng hiện tại</span>
                          <span className='font-black text-[#EBEBEB]'>{userRank.toLocaleString()}</span>
                        </div>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Màn chơi cao nhất</span>
                          <span className='font-black text-[#EBEBEB]'>{currentLevel}</span>
                        </div>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Tổng điểm</span>
                          <span className='font-black text-[#EBEBEB]'>{userTotalScore.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <Image src='/share/winShare.webp' alt='Share' fill quality={100} />

                    {/* Avatar */}
                    <div className='absolute mx-auto mt-[4%] w-[25.8%]'>
                      <AspectRatio ratio={1}>
                        <div className='relative h-full w-full rounded-full bg-gradient-to-b from-[#FFFF70] to-[#F6B936] p-[3px]'>
                          <div className='h-full w-full rounded-full'>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                              <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
                            </Avatar>
                          </div>
                        </div>
                      </AspectRatio>
                    </div>

                    {/* Texts below avatar */}
                    <div className='absolute top-[47.5%] z-10 flex items-center'>
                      <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
                        {username}
                      </span>
                    </div>
                    <div className='absolute top-[61%] z-10 flex items-center'>
                      <span className='font-signwriter text-[23.52px] text-[#FFFF70]'>Màn {level}</span>
                    </div>
                    <div className='absolute top-[73%] z-10 flex flex-col items-center'>
                      <span className='font-montserrat translate-y-[75%] text-[9.8px] font-black text-white'>
                        Score
                      </span>
                      <span className='font-montserrat text-[32.34px] font-black text-[#FFFF70] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25),-0.98px_-0.98px_3.04px_rgba(0,0,0,0.21),0.98px_0.98px_0px_rgba(255,255,255,0.55)]'>
                        {score}
                      </span>
                    </div>
                  </>
                )}
              </AspectRatio>
            </div>
          </AspectRatio>
        </div>

        {/* Bottom Container */}
        <div className='absolute bottom-0 z-10 flex h-[22%] w-full flex-col items-center'>
          <textarea
            value={text}
            onChange={handleChange}
            maxLength={100}
            placeholder='Cảm nghĩ của bạn...'
            className={`font-montserrat h-[39.2%] w-[92.5%] resize-none rounded-2xl border bg-white/10 px-[3%] py-[3%] text-[16px] tracking-tight text-white backdrop-blur-xs outline-none placeholder:text-[#BAB9B9] ${
              text.length > 0 ? 'border-[#F6B936]' : 'border-white/30'
            }`}
          />

          {/* Bottom Row */}
          <div className='flex h-[60.8%] w-full items-center justify-center space-x-[10%]'>
            <div onClick={onClose} className='relative w-[35%] cursor-pointer'>
              <AspectRatio ratio={137 / 48} className='relative'>
                <Image src='/share/mediumerOrangeButton.png' alt='Close' fill quality={100} />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Đóng
              </span>
            </div>

            <div className='relative w-[35%]' onClick={text.length > 0 ? handleExport : undefined}>
              <AspectRatio ratio={137 / 48} className='relative'>
                {text.length > 0 ? (
                  <MediumGreenButton className='h-full w-full' />
                ) : (
                  <MediumDisabledButton className='h-full w-full' />
                )}
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Chia sẻ
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ShareDialog
