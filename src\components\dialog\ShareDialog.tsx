'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Separator } from '@/components/ui/separator'
import Image from 'next/image'
import MediumDisabledButton from '@/assets/drawer/mediumDisabledButton.svg'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { useState, useRef } from 'react'
import * as htmlToImage from 'html-to-image'
import { useGameStore } from '@/stores/useGameStore'
import { useShareDialogStore } from '@/stores/useShareDialogStore'
import { useRankingGameCampaign } from '@/hooks/useRankingGameCampaign'
import { PostMessageType } from '@/lib/postMessage'

interface ShareDialogProps {
  open: boolean
  onClose: () => void
  level: number
}

const ShareDialog: React.FC<ShareDialogProps> = ({ open, onClose, level }) => {
  const [text, setText] = useState('')
  const { gameCampaignId, username, avatar, passedLevels, currentLevel } = useGameStore()
  const targetRef = useRef<HTMLDivElement>(null) // reference for AspectRatio

  // Ensure fonts are loaded for canvas export
  const ensureFontsLoaded = async () => {
    if ('fonts' in document) {
      try {
        await document.fonts.ready
        // Force load specific fonts if available
        await Promise.all(
          [
            document.fonts.load('400 16px "BD Street Sign Sans"'),
            document.fonts.load('400 16px "UTM AS Signwriter"'),
            document.fonts.load('400 16px "Montserrat"')
          ].map((p) => p.catch(() => {}))
        ) // Ignore errors for missing fonts
      } catch (error) {
        console.warn('Font loading failed:', error)
      }
    }
  }

  const currentLevelInfo = passedLevels.find((lvl) => lvl.level === level)
  const score = currentLevelInfo?.score ?? 0

  const { variant } = useShareDialogStore()

  // Get ranking data for dynamic content in rank variant
  const userId = 'x391d6b4ecadb6625af716fe675cc21cf' // TODO: get from auth/session
  const { data: rankingData } = useRankingGameCampaign(userId)

  // Find current user's ranking data
  const currentUserRanking = rankingData?.find((user) => user.name === username)
  const userRank = currentUserRanking?.rank ?? 0
  const userTotalScore = currentUserRanking?.score?.total ?? 0

  const handleExport = async () => {
    if (!targetRef.current) return
    try {
      // Preload background images to ensure they're available for canvas export
      const imagesToPreload = [variant === 'rank' ? '/share/rankShare.webp' : '/share/winShare.webp']

      await Promise.all(
        imagesToPreload.map((src) => {
          return new Promise<void>((resolve, reject) => {
            const img = document.createElement('img')
            img.crossOrigin = 'anonymous'
            img.onload = () => resolve()
            img.onerror = () => reject(new Error(`Failed to load image: ${src}`))
            img.src = src
          })
        })
      )

      // Ensure fonts are loaded
      await ensureFontsLoaded()

      // Wait a bit more for fonts and layout to settle
      await new Promise((resolve) => setTimeout(resolve, 300))

      // Enhanced options for better image export
      const dataUrl = await htmlToImage.toPng(targetRef.current, {
        cacheBust: true,
        quality: 1.0,
        pixelRatio: 2, // Higher resolution for better quality
        backgroundColor: 'transparent',
        skipFonts: false, // Include custom fonts
        width: targetRef.current.offsetWidth,
        height: targetRef.current.offsetHeight
      })

      const base64Only = dataUrl.replace(/^data:image\/png;base64,/, '')

      // Prepare message data based on variant
      const messageData = {
        type: PostMessageType.SHARE_COMMUNITY,
        gameCampaignId,
        levelNumber: variant === 'rank' ? 0 : level,
        content: text,
        imageBase64: base64Only,
        shareType: variant === 'rank' ? 'BY_RANK' : 'BY_LEVEL'
      }

      // Send message to native app
      if (window.ReactNativeWebView?.postMessage) {
        window.ReactNativeWebView.postMessage(JSON.stringify(messageData))
      }

      console.log('Share message sent:', messageData)

      // Close dialog after successful share
      onClose()
    } catch (err) {
      console.error('Export failed', err)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value.slice(0, 100) // max 100 chars
    setText(value)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className='h-full border-none bg-transparent p-0 shadow-none'
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        {/* Top Bar */}
        <div className='absolute top-0 z-10 flex h-[12.3%] w-full items-end justify-center'>
          <span className='font-bdstreet text-border-orange text-[32px]'>Chia sẻ cộng đồng</span>
        </div>

        {/* Main Center Content */}
        <div className='flex h-full w-full items-center justify-center'>
          <AspectRatio ratio={1560 / 2184} className='relative flex justify-center'>
            <Image src='/share/shareBG.png' alt='Share' fill quality={100} />

            {/* Inner AspectRatio centered vertically */}
            <div className='absolute inset-0 flex flex-col items-center justify-center'>
              <AspectRatio
                ref={targetRef}
                ratio={1560 / 1076}
                className='relative flex flex-col items-center shadow-[3px_4px_150px_15px_rgba(255,255,112,0.3)]'
                style={{
                  fontFamily: 'var(--font-bd-street), var(--font-signwriter), var(--font-montserrat), sans-serif'
                }}
              >
                {/* different background already set above; inner content changes by variant */}
                {variant === 'rank' ? (
                  <>
                    <Image src='/share/rankShare.webp' alt='Share' fill quality={100} />

                    {/* Avatar */}
                    <div className='absolute left-[6%] mt-[6%] w-[12%]'>
                      <AspectRatio ratio={1}>
                        <div className='relative h-full w-full rounded-full bg-gradient-to-b from-[#FFFF70] to-[#F6B936] p-[2px]'>
                          <div className='h-full w-full rounded-full'>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                              <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
                            </Avatar>
                          </div>
                        </div>
                      </AspectRatio>
                    </div>

                    <div className='absolute top-[8.5%] left-[22.5%] z-10 flex h-[17%] items-center'>
                      <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
                        {username}
                      </span>
                    </div>

                    <div className='font-montserrat absolute top-[34.49%] z-10 flex h-[50.6%] w-[91.81%] flex-col items-center'>
                      {/* Top Section */}
                      <div className='flex h-[29%] w-[91.66%] items-center'>
                        <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[13.72px] font-bold text-transparent'>
                          Thành tích
                        </span>
                      </div>

                      {/* Separator */}
                      <Separator orientation='horizontal' className='w-[91.66%] bg-[#535548]' />

                      {/* Bottom Section */}
                      <div className='flex h-[71%] w-[91.66%] flex-col justify-evenly text-[13.72px]'>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Hạng hiện tại</span>
                          <span className='font-black text-[#EBEBEB]'>{userRank.toLocaleString()}</span>
                        </div>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Màn chơi cao nhất</span>
                          <span className='font-black text-[#EBEBEB]'>{currentLevel}</span>
                        </div>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Tổng điểm</span>
                          <span className='font-black text-[#EBEBEB]'>{userTotalScore.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <Image src='/share/winShare.webp' alt='Share' fill quality={100} />

                    {/* Avatar */}
                    <div className='absolute mx-auto mt-[4%] w-[25.8%]'>
                      <AspectRatio ratio={1}>
                        <div className='relative h-full w-full rounded-full bg-gradient-to-b from-[#FFFF70] to-[#F6B936] p-[3px]'>
                          <div className='h-full w-full rounded-full'>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                              <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
                            </Avatar>
                          </div>
                        </div>
                      </AspectRatio>
                    </div>

                    {/* Texts below avatar */}
                    <div className='absolute top-[47.5%] z-10 flex items-center'>
                      <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
                        {username}
                      </span>
                    </div>
                    <div className='absolute top-[61%] z-10 flex items-center'>
                      <span className='font-signwriter text-[23.52px] text-[#FFFF70]'>Màn {level}</span>
                    </div>
                    <div className='absolute top-[73%] z-10 flex flex-col items-center'>
                      <span className='font-montserrat translate-y-[75%] text-[9.8px] font-black text-white'>
                        Score
                      </span>
                      <span className='font-montserrat text-[32.34px] font-black text-[#FFFF70] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25),-0.98px_-0.98px_3.04px_rgba(0,0,0,0.21),0.98px_0.98px_0px_rgba(255,255,255,0.55)]'>
                        {score}
                      </span>
                    </div>
                  </>
                )}
              </AspectRatio>
            </div>
          </AspectRatio>
        </div>

        {/* Bottom Container */}
        <div className='absolute bottom-0 z-10 flex h-[22%] w-full flex-col items-center'>
          <textarea
            value={text}
            onChange={handleChange}
            maxLength={100}
            placeholder='Cảm nghĩ của bạn...'
            className={`font-montserrat h-[39.2%] w-[92.5%] resize-none rounded-2xl border bg-white/10 px-[3%] py-[3%] text-[16px] tracking-tight text-white backdrop-blur-xs outline-none placeholder:text-[#BAB9B9] ${
              text.length > 0 ? 'border-[#F6B936]' : 'border-white/30'
            }`}
          />

          {/* Bottom Row */}
          <div className='flex h-[60.8%] w-full items-center justify-center space-x-[10%]'>
            <div onClick={onClose} className='relative w-[35%] cursor-pointer'>
              <AspectRatio ratio={137 / 48} className='relative'>
                <Image src='/share/mediumerOrangeButton.png' alt='Close' fill quality={100} />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Đóng
              </span>
            </div>

            <div className='relative w-[35%]' onClick={text.length > 0 ? handleExport : undefined}>
              <AspectRatio ratio={137 / 48} className='relative'>
                {text.length > 0 ? (
                  <MediumGreenButton className='h-full w-full' />
                ) : (
                  <MediumDisabledButton className='h-full w-full' />
                )}
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Chia sẻ
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ShareDialog
