import { NextRequest, NextResponse } from 'next/server'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface VerifyPassedLevelRequest {
  accessKey: string
  userId: string
  level: number
}

interface VerifyPassedLevelResponse {
  isWinning: boolean
}

export async function POST(req: NextRequest) {
  try {
    const body: VerifyPassedLevelRequest = await req.json()
    console.log('Verify Passed Level API - Incoming Request:', body)

    // Validate required fields
    if (!body.accessKey || !body.userId || typeof body.level !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields: accessKey, userId, level' },
        { status: 400 }
      )
    }

    // Validate access key
    if (body.accessKey !== 'abcdefg') {
      return NextResponse.json(
        { error: 'Invalid access key' },
        { status: 401 }
      )
    }

    // Check if user exists and has passed the specified level
    const isWinning = UserLevelManager.hasUserPassedLevel(body.userId, body.level)

    const response: VerifyPassedLevelResponse = {
      isWinning
    }

    console.log('Verify Passed Level API - Response:', response)
    return NextResponse.json(response)

  } catch (error) {
    console.error('Verify Passed Level API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
