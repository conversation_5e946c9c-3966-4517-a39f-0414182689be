import { NextResponse } from 'next/server'
import axios from 'axios'

export async function POST(req: Request) {
  const body = await req.json()

  try {
    const { data } = await axios.post(
      'http://192.168.88.32:17100/api/v5/api-asker-vn/get-ranking-game-campaign',
      body,
      {
        headers: {
          accessKey: '****************************************************************',
          'Content-Type': 'application/json'
        }
      }
    )
    return NextResponse.json(data)
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: error.response?.status || 500 })
  }
}
