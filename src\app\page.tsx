'use client'

import React, { useState, useEffect } from 'react'
import LoadingScreen from '@/pages/LoadingScreen'
import HomeScreen from '@/pages/HomeScreen'
import GameScreen from '@/pages/GameScreen'
import { useLanguageStore } from '@/stores/useLanguageStore'
import { useNativeToken } from '@/hooks/useNativeToken'

declare global {
  interface Window {
    ReactNativeWebView?: {
      postMessage: (message: string) => void
    }
  }
}

export default function Page() {
  const [loaded, setLoaded] = useState(false)
  const [error, setError] = useState(false)
  const [selectedStage, setSelectedStage] = useState<number | null>(null)
  const setLanguage = useLanguageStore((state) => state.setLanguage)

  // Initialize native token listener at app level so it persists throughout the app lifecycle
  useNativeToken(5000)

  useEffect(() => {
    setLanguage('VN')
  }, [setLanguage])

  if (error) {
    return (
      <div className='flex h-screen flex-col items-center justify-center text-red-500'>
        <p>Failed to load game data.</p>
        <button
          onClick={() => {
            setError(false)
            setLoaded(false)
          }}
          className='mt-4 rounded bg-orange-500 px-4 py-2 text-white'
        >
          Retry
        </button>
      </div>
    )
  }

  if (selectedStage !== null) {
    return <GameScreen stage={selectedStage} onBack={() => setSelectedStage(null)} />
  }

  return loaded ? (
    <HomeScreen onStageClick={(stage: number) => setSelectedStage(stage)} />
  ) : (
    <LoadingScreen onSuccess={() => setLoaded(true)} onError={() => setError(true)} />
  )
}
