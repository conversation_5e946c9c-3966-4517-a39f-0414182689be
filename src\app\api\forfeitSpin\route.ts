// src/app/api/forfeitSpin/route.ts
import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'

export interface ForfeitSpinRequest {
  action: 'FORFEIT_SPIN' | 'RETRY'
  levelNumber: number
  gameCampaignId: string
  userId: string
}

export interface ForfeitSpinResponse {
  success: boolean
  message?: string
  // Add other response fields as needed based on actual API response
}

export async function POST(req: NextRequest) {
  try {
    const body: ForfeitSpinRequest = await req.json()
    console.log('Incoming Request Body:', body)

    const { data } = await axios.post<ForfeitSpinResponse>(
      'http://192.168.88.32:27100/api/v3/event-vn/forfeit-spin',
      body,
      {
        headers: {
          accessKey: '****************************************************************',
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Forfeit spin API error:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json(
      { error: err.message || 'Failed to process forfeit spin request' },
      { status: err.response?.status ?? 500 }
    )
  }
}
