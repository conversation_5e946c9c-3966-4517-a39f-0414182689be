'use client'

import { <PERSON>er, <PERSON>er<PERSON>ontent, Drawer<PERSON>eader, Drawer<PERSON><PERSON>le, DrawerClose, DrawerFooter } from '@/components/ui/drawer'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import SuccessIcon from '@/assets/drawer/success.svg'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'
import LargeOrangeButton from '@/assets/drawer/largeOrangeButton.svg'
import Image from 'next/image'
import { PostMessageType } from '@/lib/postMessage'

interface DailyMissionDrawerProps {
  open: boolean
  onClose: () => void
}

const DailyMissionDrawer: React.FC<DailyMissionDrawerProps> = ({ open, onClose }) => {
  const handleNavigationClick = (screenName: string) => {
    try {
      if (window.ReactNativeWebView?.postMessage) {
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: PostMessageType.NAVIGATION_TO,
            screenName
          })
        )
      }
    } catch (error) {
      console.error('Failed to send navigation message to native app:', error)
    }
  }

  return (
    <Drawer open={open} onOpenChange={onClose}>
      <DrawerContent className='flex h-[97.5dvh] flex-col overflow-hidden'>
        {/* Fixed header */}
        <DrawerHeader className='h-[18.5%]'>
          <DrawerTitle className='font-bdstreet text-border-orange text-[32px]'>Nhiệm vụ hàng ngày</DrawerTitle>
        </DrawerHeader>

        {/* Scrollable content */}
        <div className='flex-1 overflow-hidden'>
          <ScrollArea className='h-full w-full'>
            <div className='font-montserrat flex flex-col space-y-[3%] p-[4%]'>
              <div className='flex flex-col gap-2 rounded-3xl border border-white/30 bg-white/10 text-white backdrop-blur-xs'>
                {/* Top Aspect Ratio (358/46) */}
                <AspectRatio ratio={358 / 46} className='relative mx-auto w-[90%]'>
                  <div className='absolute inset-0 flex items-center justify-between'>
                    {/* Left side: "Đăng nhập" + success icon */}
                    <div className='flex items-center gap-2'>
                      <p className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-semibold text-transparent'>
                        Đăng nhập
                      </p>
                      <SuccessIcon className='h-5 w-5' />
                    </div>

                    {/* Right side: secondary AspectRatio + mooncake image + "+1" */}
                    <div className='flex w-[12.5%] translate-y-[10%] items-center gap-2'>
                      <span className='font-bdstreet text-[16px] text-[#00A464]'>+1</span>
                      <div className='w-full'>
                        <AspectRatio ratio={955 / 968} className='relative -translate-y-[10%]'>
                          <Image
                            src='/dialog/mooncake.png'
                            alt='Mooncake'
                            fill
                            quality={100}
                            className='drop-shadow-[5px_2px_20px_rgba(0,0,0,0.3)]'
                          />
                        </AspectRatio>
                      </div>
                    </div>
                  </div>

                  <Separator className='absolute bottom-0 left-0 w-full bg-[#535548]' />
                </AspectRatio>
                <AspectRatio ratio={358 / 46} className='relative mx-auto flex w-[90%] items-center'>
                  <p className='-translate-y-[20%] text-[14px] font-medium text-white'>Truy cập game và điểm danh</p>
                </AspectRatio>
              </div>
              <div className='relative rounded-3xl border border-white/30 bg-white/10 text-white backdrop-blur-xs'>
                <AspectRatio ratio={358 / 46} className='relative mx-auto w-[90%]'>
                  <div className='absolute inset-0 flex items-center justify-between'>
                    {/* Left side: "Đăng nhập" + success icon */}
                    <div className='flex items-center gap-2'>
                      <p className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-semibold text-transparent'>
                        Chia sẻ thành tích
                      </p>
                      <SuccessIcon className='h-5 w-5' />
                    </div>

                    {/* Right side: secondary AspectRatio + mooncake image + "+1" */}
                    <div className='flex w-[12.5%] translate-y-[10%] items-center gap-2'>
                      <span className='font-bdstreet text-[16px] text-[#00A464]'>+1</span>
                      <div className='w-full'>
                        <AspectRatio ratio={955 / 968} className='relative -translate-y-[10%]'>
                          <Image
                            src='/dialog/mooncake.png'
                            alt='Mooncake'
                            fill
                            quality={100}
                            className='drop-shadow-[5px_2px_20px_rgba(0,0,0,0.3)]'
                          />
                        </AspectRatio>
                      </div>
                    </div>
                  </div>

                  <Separator className='absolute bottom-0 left-0 w-full bg-[#535548]' />
                </AspectRatio>

                <p className='pt-[5%] pr-[15%] pb-[2%] pl-[5%] text-[14px] font-medium text-white'>
                  Chia sẻ thành công thành tích trên Bảng xếp hạng lên Cộng đồng
                </p>

                {/* Button at bottom-right */}
                <div className='mb-[2%] flex justify-end'>
                  <div className='mr-[5%] w-[25%]' onClick={() => handleNavigationClick('UserRewards')}>
                    <AspectRatio ratio={137 / 49} className='relative cursor-pointer'>
                      <MediumGreenButton className='absolute inset-0 h-full w-full' />
                      <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[14px] text-white'>
                        Đến ngay
                      </span>
                    </AspectRatio>
                  </div>
                </div>
              </div>

              <div className='relative rounded-3xl border border-white/30 bg-white/10 text-white backdrop-blur-xs'>
                <AspectRatio ratio={358 / 46} className='relative mx-auto w-[90%]'>
                  <div className='absolute inset-0 flex items-center justify-between'>
                    {/* Left side: "Đăng nhập" + success icon */}
                    <div className='flex items-center gap-2'>
                      <p className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-semibold text-transparent'>
                        Đổi deal Rewards
                      </p>
                      <SuccessIcon className='h-5 w-5' />
                    </div>

                    {/* Right side: secondary AspectRatio + mooncake image + "+1" */}
                    <div className='flex w-[12.5%] translate-y-[10%] items-center gap-2'>
                      <span className='font-bdstreet text-[16px] text-[#00A464]'>+1</span>
                      <div className='w-full'>
                        <AspectRatio ratio={955 / 968} className='relative -translate-y-[10%]'>
                          <Image
                            src='/dialog/mooncake.png'
                            alt='Mooncake'
                            fill
                            quality={100}
                            className='drop-shadow-[5px_2px_20px_rgba(0,0,0,0.3)]'
                          />
                        </AspectRatio>
                      </div>
                    </div>
                  </div>

                  <Separator className='absolute bottom-0 left-0 w-full bg-[#535548]' />
                </AspectRatio>

                <p className='pt-[5%] pr-[15%] pb-[2%] pl-[5%] text-[14px] font-medium text-white'>
                  Đổi thành công 1 deal bất kỳ tại mục bRewards trên ứng dụng bTaskee.
                </p>

                {/* Button at bottom-right */}
                <div className='mb-[2%] flex justify-end'>
                  <div className='mr-[5%] w-[25%]' onClick={() => handleNavigationClick('ExploreBTaskee')}>
                    <AspectRatio ratio={137 / 49} className='relative cursor-pointer'>
                      <MediumGreenButton className='absolute inset-0 h-full w-full' />
                      <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[14px] text-white'>
                        Đến ngay
                      </span>
                    </AspectRatio>
                  </div>
                </div>
              </div>
              <div className='relative rounded-3xl border border-white/30 bg-white/10 text-white backdrop-blur-xs'>
                <AspectRatio ratio={358 / 46} className='relative mx-auto w-[90%]'>
                  <div className='absolute inset-0 flex items-center justify-between'>
                    {/* Left side: "Đăng nhập" + success icon */}
                    <div className='flex items-center gap-2'>
                      <p className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-semibold text-transparent'>
                        Hoàn thành đơn hàng
                      </p>
                      <SuccessIcon className='h-5 w-5' />
                    </div>

                    {/* Right side: secondary AspectRatio + mooncake image + "+1" */}
                    <div className='flex w-[12.5%] translate-y-[10%] items-center gap-2'>
                      <span className='font-bdstreet text-[16px] text-[#00A464]'>+2</span>
                      <div className='w-full'>
                        <AspectRatio ratio={955 / 968} className='relative -translate-y-[10%]'>
                          <Image
                            src='/dialog/mooncake.png'
                            alt='Mooncake'
                            fill
                            quality={100}
                            className='drop-shadow-[5px_2px_20px_rgba(0,0,0,0.3)]'
                          />
                        </AspectRatio>
                      </div>
                    </div>
                  </div>

                  <Separator className='absolute bottom-0 left-0 w-full bg-[#535548]' />
                </AspectRatio>

                <p className='pt-[5%] pr-[15%] pb-[2%] pl-[5%] text-[14px] font-medium text-white'>
                  Hoàn tất dịch vụ bất kỳ trên ứng dụng bTaskee
                </p>

                {/* Button at bottom-right */}
                <div className='mb-[2%] flex justify-end'>
                  <div className='mr-[5%] w-[25%]'>
                    <AspectRatio ratio={137 / 49} className='relative'>
                      <MediumGreenButton className='absolute inset-0 h-full w-full' />
                      <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[14px] text-white'>
                        Đến ngay
                      </span>
                    </AspectRatio>
                  </div>
                </div>
              </div>
              <div className='relative rounded-3xl border border-white/30 bg-white/10 text-white backdrop-blur-xs'>
                <AspectRatio ratio={358 / 46} className='relative mx-auto w-[90%]'>
                  <div className='absolute inset-0 flex items-center justify-between'>
                    {/* Left side: "Đăng nhập" + success icon */}
                    <div className='flex items-center gap-2'>
                      <p className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[16px] font-semibold text-transparent'>
                        Giới thiệu bạn bè
                      </p>
                      <SuccessIcon className='h-5 w-5' />
                    </div>

                    {/* Right side: secondary AspectRatio + mooncake image + "+1" */}
                    <div className='flex w-[12.5%] translate-y-[10%] items-center gap-2'>
                      <span className='font-bdstreet text-[16px] text-[#00A464]'>+2</span>
                      <div className='w-full'>
                        <AspectRatio ratio={955 / 968} className='relative -translate-y-[10%]'>
                          <Image
                            src='/dialog/mooncake.png'
                            alt='Mooncake'
                            fill
                            quality={100}
                            className='drop-shadow-[5px_2px_20px_rgba(0,0,0,0.3)]'
                          />
                        </AspectRatio>
                      </div>
                    </div>
                  </div>

                  <Separator className='absolute bottom-0 left-0 w-full bg-[#535548]' />
                </AspectRatio>

                <p className='pt-[5%] pr-[15%] pb-[3%] pl-[5%] text-[14px] font-medium text-white'>
                  Bạn bè đăng ký tài khoản thành công.
                </p>

                {/* Button at bottom-right */}
                <div className='mb-[2%] flex justify-end'>
                  <div className='mr-[5%] w-[25%]' onClick={() => handleNavigationClick('ShareMyFriends')}>
                    <AspectRatio ratio={137 / 49} className='relative cursor-pointer'>
                      <MediumGreenButton className='absolute inset-0 h-full w-full' />
                      <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[14px] text-white'>
                        Đến ngay
                      </span>
                    </AspectRatio>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <DrawerFooter className='flex h-[11%] items-center justify-center pb-[5%]'>
          <DrawerClose className='w-full'>
            <AspectRatio ratio={326 / 48} className='relative mx-auto w-[90%]'>
              <LargeOrangeButton className='absolute inset-0 h-full w-full' />
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                Đóng
              </span>
            </AspectRatio>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default DailyMissionDrawer
