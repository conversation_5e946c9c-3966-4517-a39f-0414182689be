'use client'

import { <PERSON>er, <PERSON>er<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON><PERSON><PERSON>, Drawer<PERSON>lose, DrawerFooter } from '@/components/ui/drawer'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { ScrollArea } from '@/components/ui/scroll-area'
import ReactMarkdown from 'react-markdown'
import LargeOrangeButton from '@/assets/drawer/largeOrangeButton.svg'
import { useState, useEffect } from 'react'

interface GuidelineDrawerProps {
  open: boolean
  onClose: () => void
}

const GuidelineDrawer: React.FC<GuidelineDrawerProps> = ({ open, onClose }) => {
  const [markdownContent, setMarkdownContent] = useState('')

  useEffect(() => {
    // Fetch the markdown content
    fetch('/guideline/vi.md')
      .then((response) => response.text())
      .then((text) => setMarkdownContent(text))
      .catch((error) => console.error('Error loading markdown:', error))
  }, [])

  return (
    <Drawer open={open} onOpenChange={onClose}>
      <DrawerContent className='flex h-[97.5dvh] flex-col overflow-hidden'>
        {/* Fixed header */}
        <DrawerHeader className='h-[18.5%]'>
          <DrawerTitle className='font-bdstreet text-border-orange text-[32px]'>Thể lệ</DrawerTitle>
        </DrawerHeader>

        {/* Scrollable content */}
        <div className='flex h-[70.5%] flex-1 items-end overflow-hidden'>
          <div className='mx-auto h-[97%] w-[91.79%] rounded-3xl border border-white/30 bg-white/10 backdrop-blur-xs'>
            <ScrollArea className='h-full w-full p-6'>
              <ReactMarkdown
                className='font-montserrat text-white'
                components={{
                  // Remove list item bullets and only indent
                  li: ({ children }) => <li className='ml-4 list-none'>{children}</li>,
                  // Style specific text with colors
                  strong: ({ children }) => {
                    const text = children?.toString() || ''
                    if (
                      text.includes('GỌI ÁNH TRĂNG VỀ') ||
                      text.includes('15/09/2025 - 15/10/2025') ||
                      text.includes('Bảng Nhiệm Vụ Hằng Ngày.')
                    ) {
                      return <strong className='text-[#FF5500]'>{children}</strong>
                    }
                    return <strong className='text-white'>{children}</strong>
                  },
                  em: ({ children }) => {
                    const text = children?.toString() || ''
                    if (
                      text.includes('"Ưu đãi của tôi"') ||
                      text.includes('1900.636.736') ||
                      text.includes('<EMAIL>')
                    ) {
                      return <em className='text-[#FA702A] not-italic'>{children}</em>
                    }
                    if (text.includes('Lưu ý: Apple không tham gia tài trợ chương trình này.')) {
                      return <em className='text-[#DACC5F] not-italic'>{children}</em>
                    }
                    return <em className='text-white'>{children}</em>
                  },
                  // Handle regular text that might contain special phrases
                  p: ({ children }) => {
                    const processText = (node: any): any => {
                      if (typeof node === 'string') {
                        // Check for special phrases and wrap them with appropriate colors
                        if (node.includes('"Ưu đãi của tôi"')) {
                          return node.replace(
                            '"Ưu đãi của tôi"',
                            '<span class="text-[#FA702A]">"Ưu đãi của tôi"</span>'
                          )
                        }
                        if (node.includes('1900.636.736')) {
                          return node.replace('1900.636.736', '<span class="text-[#FA702A]">1900.636.736</span>')
                        }
                        if (node.includes('<EMAIL>')) {
                          return node.replace(
                            '<EMAIL>',
                            '<span class="text-[#FA702A]"><EMAIL></span>'
                          )
                        }
                        if (node.includes('Lưu ý: Apple không tham gia tài trợ chương trình này.')) {
                          return node.replace(
                            'Lưu ý: Apple không tham gia tài trợ chương trình này.',
                            '<span class="text-[#DACC5F]">Lưu ý: Apple không tham gia tài trợ chương trình này.</span>'
                          )
                        }
                        return node
                      }
                      return node
                    }

                    return (
                      <p className='mb-4 text-white'>
                        {Array.isArray(children) ? (
                          children.map((child, index) => (
                            <span key={index} dangerouslySetInnerHTML={{ __html: processText(child) }} />
                          ))
                        ) : (
                          <span dangerouslySetInnerHTML={{ __html: processText(children) }} />
                        )}
                      </p>
                    )
                  }
                }}
              >
                {markdownContent}
              </ReactMarkdown>
            </ScrollArea>
          </div>
        </div>

        {/* Footer */}
        <DrawerFooter className='flex h-[11%] items-center justify-center pb-[5%]'>
          <DrawerClose className='w-full'>
            <AspectRatio ratio={326 / 48} className='relative mx-auto w-[90%]'>
              <LargeOrangeButton className='absolute inset-0 h-full w-full' />
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                Đóng
              </span>
            </AspectRatio>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default GuidelineDrawer
